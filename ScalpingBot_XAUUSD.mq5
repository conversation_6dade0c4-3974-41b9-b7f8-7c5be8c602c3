//+------------------------------------------------------------------+
//|                     Scalping Bot for XAUUSD                      |
//+------------------------------------------------------------------+
#property strict

//--- Input parameters
input double LotSize = 0.01;        // Default lot size
input int MA_Period = 12;           // Moving Average Period
input double RiskPercent = 1.0;     // Risk per trade (% of account balance)
input double TakeProfitPips = 50;   // Take profit in pips
input double StopLossPips = 20;     // Stop loss in pips
input double MaxSpread = 50;        // Maximum allowed spread (in points)
input int MagicNumber = 12345;      // Magic number for orders

//--- Global variables
double maValue;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("Scalping Bot Initialized");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check spread
    double spread = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID)) / _Point;
    if (spread > MaxSpread) return;

    //--- Calculate MA value
    maValue = iMA(_Symbol, 0, MA_Period, 0, MODE_SMA, PRICE_CLOSE, 1);

    //--- Check if there's already an open position
    if (PositionsTotal() > 0)
    {
        ManageTrailingStop();
        return;
    }

    //--- Get the last closed candle values
    double lastClose = iClose(_Symbol, 0, 1);

    //--- Determine trading conditions
    if (lastClose > maValue)
    {
        OpenPosition(ORDER_TYPE_BUY);
    }
    else if (lastClose < maValue)
    {
        OpenPosition(ORDER_TYPE_SELL);
    }
}

//+------------------------------------------------------------------+
//| Open a position                                                  |
//+------------------------------------------------------------------+
void OpenPosition(int orderType)
{
    MqlTradeRequest request;
    MqlTradeResult result;

    //--- Get account balance
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);

    //--- Calculate lot size based on the risk percentage
    double riskAmount = accountBalance * RiskPercent / 100.0;
    double lotSize = MathMin(riskAmount / (StopLossPips * _Point * SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE)), LotSize);

    //--- Set SL and TP
    double price = (orderType == ORDER_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double stopLoss = (orderType == ORDER_TYPE_BUY) ? price - StopLossPips * _Point : price + StopLossPips * _Point;
    double takeProfit = (orderType == ORDER_TYPE_BUY) ? price + TakeProfitPips * _Point : price - TakeProfitPips * _Point;

    //--- Fill request structure
    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = orderType;
    request.price = price;
    request.sl = NormalizeDouble(stopLoss, _Digits);
    request.tp = NormalizeDouble(takeProfit, _Digits);
    request.deviation = 2;
    request.magic = MagicNumber;

    //--- Send request
    if (!OrderSend(request, result))
    {
        Print("Error opening position: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Manage trailing stop                                             |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    for (int i = 0; i < PositionsTotal(); i++)
    {
        //--- Get position details
        if (PositionGetInteger(POSITION_MAGIC) != MagicNumber) continue;

        double price = PositionGetDouble(POSITION_PRICE_OPEN);
        double stopLoss = PositionGetDouble(POSITION_SL);
        double currentPrice = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        double newStopLoss = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? currentPrice - StopLossPips * _Point : currentPrice + StopLossPips * _Point;

        //--- Update SL if the new SL is better
        if ((PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY && newStopLoss > stopLoss) ||
            (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL && newStopLoss < stopLoss))
        {
            MqlTradeRequest request;
            MqlTradeResult result;

            request.action = TRADE_ACTION_SLTP;
            request.symbol = _Symbol;
            request.sl = NormalizeDouble(newStopLoss, _Digits);
            request.tp = PositionGetDouble(POSITION_TP);
            request.position = PositionGetInteger(POSITION_TICKET);

            if (!OrderSend(request, result))
            {
                Print("Error modifying position: ", GetLastError());
            }
        }
    }
}
